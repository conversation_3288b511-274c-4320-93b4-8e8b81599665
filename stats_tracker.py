#!/usr/bin/env python3
"""
Statistics Tracker for License Key Analysis
Tracks and saves statistics for each license key processed.
"""

import pandas as pd
import os
from typing import Dict, Any


class StatsTracker:
    def __init__(self, stats_file: str = "stats_per_license.csv"):
        self.stats_file = stats_file
        self.ensure_stats_file_exists()
    
    def ensure_stats_file_exists(self):
        """Create the stats file with headers if it doesn't exist."""
        if not os.path.exists(self.stats_file):
            headers = [
                'license_key',
                'buyer_names',
                'credited_count',
                'uncredited_count',
                'credited_sales',
                'uncredited_sales',
                'ratio_count_percentage',
                'ratio_sales_percentage',
                'credited_count_10min',
                'uncredited_count_10min',
                'credited_sales_10min',
                'uncredited_sales_10min',
                'ratio_count_percentage_10min',
                'ratio_sales_percentage_10min'
            ]
            
            df = pd.DataFrame(columns=headers)
            df.to_csv(self.stats_file, index=False)
            print(f"📊 Created stats file: {self.stats_file}")
    
    def calculate_stats(self, results_df: pd.DataFrame, license_key: str, buyer_names: str) -> Dict[str, Any]:
        """
        Calculate statistics from the results dataframe.
        
        Args:
            results_df: DataFrame with analysis results
            license_key: License key being processed
            buyer_names: Buyer names for this license key
            
        Returns:
            Dictionary with calculated statistics
        """
        if results_df.empty:
            return self._empty_stats(license_key, buyer_names)
        
        # Ensure required columns exist
        required_cols = ['credited', 'price', 'quantity_sold', 'duration']
        for col in required_cols:
            if col not in results_df.columns:
                print(f"⚠️ Warning: Column '{col}' not found in results")
                return self._empty_stats(license_key, buyer_names)
        
        # Calculate sales amount (price * quantity)
        results_df = results_df.copy()
        results_df['sales_amount'] = results_df['price'] * results_df['quantity_sold']
        
        # Overall statistics
        credited_mask = results_df['credited'] == 'Yes'
        uncredited_mask = results_df['credited'] == 'No'
        
        credited_count = credited_mask.sum()
        uncredited_count = uncredited_mask.sum()
        total_count = len(results_df)
        
        credited_sales = results_df[credited_mask]['sales_amount'].sum() if credited_count > 0 else 0
        uncredited_sales = results_df[uncredited_mask]['sales_amount'].sum() if uncredited_count > 0 else 0
        total_sales = credited_sales + uncredited_sales
        
        # Calculate ratios
        ratio_count_percentage = (credited_count / total_count * 100) if total_count > 0 else 0
        ratio_sales_percentage = (credited_sales / total_sales * 100) if total_sales > 0 else 0
        
        # 10-minute statistics (duration < 600 seconds)
        ten_min_mask = results_df['duration'] < 600
        ten_min_df = results_df[ten_min_mask]
        
        if len(ten_min_df) > 0:
            credited_10min_mask = ten_min_df['credited'] == 'Yes'
            uncredited_10min_mask = ten_min_df['credited'] == 'No'
            
            credited_count_10min = credited_10min_mask.sum()
            uncredited_count_10min = uncredited_10min_mask.sum()
            total_count_10min = len(ten_min_df)
            
            credited_sales_10min = ten_min_df[credited_10min_mask]['sales_amount'].sum() if credited_count_10min > 0 else 0
            uncredited_sales_10min = ten_min_df[uncredited_10min_mask]['sales_amount'].sum() if uncredited_count_10min > 0 else 0
            total_sales_10min = credited_sales_10min + uncredited_sales_10min
            
            ratio_count_percentage_10min = (credited_count_10min / total_count_10min * 100) if total_count_10min > 0 else 0
            ratio_sales_percentage_10min = (credited_sales_10min / total_sales_10min * 100) if total_sales_10min > 0 else 0
        else:
            credited_count_10min = uncredited_count_10min = 0
            credited_sales_10min = uncredited_sales_10min = 0
            ratio_count_percentage_10min = ratio_sales_percentage_10min = 0
        
        return {
            'license_key': license_key,
            'buyer_names': buyer_names,
            'credited_count': int(credited_count),
            'uncredited_count': int(uncredited_count),
            'credited_sales': round(credited_sales, 2),
            'uncredited_sales': round(uncredited_sales, 2),
            'ratio_count_percentage': round(ratio_count_percentage, 2),
            'ratio_sales_percentage': round(ratio_sales_percentage, 2),
            'credited_count_10min': int(credited_count_10min),
            'uncredited_count_10min': int(uncredited_count_10min),
            'credited_sales_10min': round(credited_sales_10min, 2),
            'uncredited_sales_10min': round(uncredited_sales_10min, 2),
            'ratio_count_percentage_10min': round(ratio_count_percentage_10min, 2),
            'ratio_sales_percentage_10min': round(ratio_sales_percentage_10min, 2)
        }
    
    def _empty_stats(self, license_key: str, buyer_names: str) -> Dict[str, Any]:
        """Return empty statistics for cases with no results."""
        return {
            'license_key': license_key,
            'buyer_names': buyer_names,
            'credited_count': 0,
            'uncredited_count': 0,
            'credited_sales': 0.0,
            'uncredited_sales': 0.0,
            'ratio_count_percentage': 0.0,
            'ratio_sales_percentage': 0.0,
            'credited_count_10min': 0,
            'uncredited_count_10min': 0,
            'credited_sales_10min': 0.0,
            'uncredited_sales_10min': 0.0,
            'ratio_count_percentage_10min': 0.0,
            'ratio_sales_percentage_10min': 0.0
        }
    
    def add_stats(self, stats: Dict[str, Any]):
        """
        Add statistics to the CSV file.
        
        Args:
            stats: Dictionary with statistics to add
        """
        try:
            # Read existing data
            if os.path.exists(self.stats_file):
                df = pd.read_csv(self.stats_file)
            else:
                df = pd.DataFrame()
            
            # Add new row
            new_row = pd.DataFrame([stats])
            df = pd.concat([df, new_row], ignore_index=True)
            
            # Save back to file
            df.to_csv(self.stats_file, index=False)
            
            print(f"📊 Added stats for {stats['license_key']}: "
                  f"Credited: {stats['credited_count']}/{stats['credited_count'] + stats['uncredited_count']} "
                  f"({stats['ratio_count_percentage']:.1f}%), "
                  f"Sales: ${stats['credited_sales']:.2f}/${stats['credited_sales'] + stats['uncredited_sales']:.2f} "
                  f"({stats['ratio_sales_percentage']:.1f}%)")
            
        except Exception as e:
            print(f"❌ Error adding stats: {e}")
    
    def print_summary(self):
        """Print a summary of all statistics."""
        try:
            if not os.path.exists(self.stats_file):
                print("📊 No stats file found")
                return
            
            df = pd.read_csv(self.stats_file)
            if df.empty:
                print("📊 No statistics recorded yet")
                return
            
            print(f"\n📊 Statistics Summary ({len(df)} license keys processed):")
            print("=" * 80)
            
            # Overall totals
            total_credited = df['credited_count'].sum()
            total_uncredited = df['uncredited_count'].sum()
            total_items = total_credited + total_uncredited
            
            total_credited_sales = df['credited_sales'].sum()
            total_uncredited_sales = df['uncredited_sales'].sum()
            total_sales = total_credited_sales + total_uncredited_sales
            
            print(f"Overall Totals:")
            print(f"  Items: {total_credited:,} credited / {total_items:,} total ({total_credited/total_items*100:.1f}%)")
            print(f"  Sales: ${total_credited_sales:,.2f} credited / ${total_sales:,.2f} total ({total_credited_sales/total_sales*100:.1f}%)")
            
            # 10-minute totals
            total_credited_10min = df['credited_count_10min'].sum()
            total_uncredited_10min = df['uncredited_count_10min'].sum()
            total_items_10min = total_credited_10min + total_uncredited_10min
            
            total_credited_sales_10min = df['credited_sales_10min'].sum()
            total_uncredited_sales_10min = df['uncredited_sales_10min'].sum()
            total_sales_10min = total_credited_sales_10min + total_uncredited_sales_10min
            
            if total_items_10min > 0:
                print(f"\n10-Minute Totals:")
                print(f"  Items: {total_credited_10min:,} credited / {total_items_10min:,} total ({total_credited_10min/total_items_10min*100:.1f}%)")
                print(f"  Sales: ${total_credited_sales_10min:,.2f} credited / ${total_sales_10min:,.2f} total ({total_credited_sales_10min/total_sales_10min*100:.1f}%)")
            
        except Exception as e:
            print(f"❌ Error printing summary: {e}")
