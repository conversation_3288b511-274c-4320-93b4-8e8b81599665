-- This script tracks the full journey of an item for a specific license key prefix and buyer.
-- It shows the time the item was sent, when the user clicked, and when it was ultimately sold.
-- It identifies the EARLIEST user action for each item and license key combination.

WITH ranked_item_actions AS (
    -- For each item and license key, find all associated user clicks/purchases
    -- and rank them by time to identify the earliest action.
    SELECT
        srv.item_id,
        pushed_key.key AS license_key,
        si.buyer_name,
        srv.found_time,
        cp.action_time,
        srv.start_datetime,
        si.end_time,
        ROW_NUMBER() OVER(PARTITION BY srv.item_id, pushed_key.key ORDER BY cp.action_time ASC) as action_rank
    FROM
        server_items srv
        -- Unnest the pushed_to_users array to filter by individual license keys.
        CROSS JOIN LATERAL unnest(srv.pushed_to_users) AS pushed_key(key)
    -- Join with clicks_and_purchases to find the user's interaction time.
    LEFT JOIN
        clicks_and_purchases cp ON srv.item_id = cp.item_id AND cp.license_key = pushed_key.key AND cp.action_time > '2025-06-01' AND cp.action_time < '2025-07-01'
    -- Join with sold_items to get the final sale information, filtering for the specific buyer.
    JOIN
        sold_items si ON srv.item_id = si.item_id
        AND si.buyer_name = 'le'
    -- Join with epn_transactions to ensure the data is complete, linking by item and a partial key.
    LEFT JOIN
        epn_transactions et ON srv.item_id = et.itemid AND et.customid_partial_license_key = LEFT(pushed_key.key, 9) AND et.eventdate >= '2025-06-01' AND et.eventdate < '2025-07-01'
    WHERE
        -- Filter for items pushed with the specified license key prefix.
        pushed_key.key LIKE '3A3E%'
        -- and limit to sales in June 2025
        AND si.end_time >= '2025-06-01' AND si.end_time < '2025-07-01'
        AND srv.start_datetime > '2025-06-01' AND srv.start_datetime < '2025-07-01'
)
-- Select the final data, filtered to include only the earliest action.
SELECT
    item_id,
    license_key,
    buyer_name,
    found_time,
    -- Calculate the interval between the item being sent and the user's action.
    COALESCE(EXTRACT(EPOCH FROM (action_time - start_datetime)), 0) - found_time AS time_clicked,
    -- Calculate the interval between the user's action and the sale time.
    COALESCE(EXTRACT(EPOCH FROM (end_time - action_time)), 0) AS time_sold,
    start_datetime,
    action_time,
    end_time
FROM
    ranked_item_actions
WHERE
    -- Filter for the earliest action (or no action, where action_rank would be 1 on a NULL action_time).
    action_rank = 1
ORDER BY
    start_datetime DESC;