# Database Structure

## `categories`

| Column Name | Data Type | Max Length | Is Nullable | Default |
|---|---|---|---|---|
| id | integer | | YES | |
| id_path | character varying | 255 | YES | |
| name_path | character varying | 255 | YES | |

## `clicks_and_purchases`

| Column Name | Data Type | Max Length | Is Nullable | Default |
|---|---|---|---|---|
| action_time | timestamp without time zone | | YES | |
| item_id | bigint | | YES | |
| action_type | character varying | 8000 | YES | |
| hwid | character varying | 8000 | YES | |
| license_key | character varying | 8000 | YES | |
| amount | numeric | | YES | |
| id | bigint | | NO | nextval('clicks_and_purchases_id_seq'::regclass) |
| created_at | timestamp with time zone | | YES | CURRENT_TIMESTAMP |

## `epn_transactions`

| Column Name | Data Type | Max Length | Is Nullable | Default |
|---|---|---|---|---|
| eventdate | timestamp without time zone | | YES | |
| campaignid | bigint | | YES | |
| customid | character varying | 255 | YES | |
| itemid | bigint | | YES | |
| quantity | integer | | YES | |
| sales | numeric | | YES | |
| earnings | numeric | | YES | |
| metacategoryid | integer | | YES | |
| leafcategoryid | integer | | YES | |
| epntransactionid | bigint | | YES | |
| clicktimestamp | timestamp without time zone | | YES | |
| customid_partial_license_key | character varying | 4 | YES | |

## `invalid_items`

| Column Name | Data Type | Max Length | Is Nullable | Default |
|---|---|---|---|---|
| item_id | bigint | | NO | |
| fetch_time | timestamp without time zone | | YES | |

## `server_items`

| Column Name | Data Type | Max Length | Is Nullable | Default |
|---|---|---|---|---|
| category_id | integer | | YES | |
| item_id | bigint | | NO | |
| start_datetime | timestamp without time zone | | YES | |
| end_datetime | timestamp without time zone | | YES | |
| sold_datetime | timestamp without time zone | | YES | |
| found_time | integer | | YES | |
| sold_time | integer | | YES | |
| price | numeric | | YES | |
| pushed_to_users | ARRAY | | YES | |

## `sold_items`

| Column Name | Data Type | Max Length | Is Nullable | Default |
|---|---|---|---|---|
| item_id | bigint | | NO | |
| leaf_category_id | integer | | YES | |
| start_time | timestamp without time zone | | YES | |
| end_time | timestamp without time zone | | YES | |
| price | numeric | | YES | |
| quantity_sold | integer | | YES | |
| duration | integer | | YES | |
| listing_status | smallint | | YES | |
| fetch_time | timestamp without time zone | | YES | |
| buyer_name | character varying | 2 | YES | |
| buyer_feedback_count | integer | | YES | |
| seller_name | character varying | 40 | YES | |
| seller_feedback_count | integer | | YES | |
| buyer_feedback_private | boolean | | YES | |
