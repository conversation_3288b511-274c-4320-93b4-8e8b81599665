#!/usr/bin/env python3
"""
Performance Benchmark Script
Compares original vs optimized data processing performance.
"""

import time
import pandas as pd
import numpy as np
from data_processor import DataProcessor
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple


class PerformanceBenchmark:
    def __init__(self):
        self.processor = DataProcessor()
        self.results = {}
    
    def generate_test_data(self, num_rows: int = 1000) -> Dict[str, pd.DataFrame]:
        """Generate synthetic test data for benchmarking."""
        print(f"🔧 Generating test data with {num_rows} rows...")
        
        # Generate server items data
        server_data = {
            'item_id': np.random.randint(100000, 999999, num_rows),
            'start_datetime': pd.date_range('2025-01-01', periods=num_rows, freq='1H'),
            'end_time': pd.date_range('2025-01-02', periods=num_rows, freq='1H'),
            'found_time': np.random.uniform(0, 300, num_rows),
            'pushed_to_users': [
                str(np.random.choice(['0722-725C-ABC', '0722-725C-DEF', '0722-725C-GHI'], 
                                   size=np.random.randint(1, 5)).tolist())
                for _ in range(num_rows)
            ]
        }
        server_df = pd.DataFrame(server_data)
        
        # Generate clicks and purchases data
        clicks_data = {
            'item_id': np.random.choice(server_data['item_id'], num_rows // 2),
            'license_key': np.random.choice(['0722-725C-ABC', '0722-725C-DEF'], num_rows // 2),
            'action_time': pd.date_range('2025-01-01 12:00', periods=num_rows // 2, freq='2H'),
            'action_type': np.random.choice(['click', 'purchase'], num_rows // 2)
        }
        clicks_df = pd.DataFrame(clicks_data)
        
        # Generate EPN transactions data
        epn_data = {
            'item_id': np.random.choice(server_data['item_id'], num_rows // 3),
            'customid_itemid': np.random.choice(server_data['item_id'], num_rows // 3),
            'customid_partial_license_key': ['0722-725C'] * (num_rows // 3),
            'eventdate': pd.date_range('2025-01-01 18:00', periods=num_rows // 3, freq='3H')
        }
        epn_df = pd.DataFrame(epn_data)
        
        return {
            'server_items': server_df,
            'clicks_purchases': clicks_df,
            'epn_transactions': epn_df
        }
    
    def benchmark_unnest_pushed_to_users(self, server_df: pd.DataFrame, license_prefix: str = '0722-725C') -> Dict:
        """Benchmark the unnest_pushed_to_users method."""
        print("🔍 Benchmarking unnest_pushed_to_users...")
        
        # Warm up
        _ = self.processor.unnest_pushed_to_users(server_df.head(10), license_prefix)
        
        # Benchmark
        start_time = time.time()
        result = self.processor.unnest_pushed_to_users(server_df, license_prefix)
        end_time = time.time()
        
        execution_time = end_time - start_time
        rows_processed = len(server_df)
        rows_output = len(result)
        
        return {
            'method': 'unnest_pushed_to_users',
            'execution_time': execution_time,
            'rows_input': rows_processed,
            'rows_output': rows_output,
            'rows_per_second': rows_processed / execution_time if execution_time > 0 else 0
        }
    
    def benchmark_join_epn_transactions(self, combined_df: pd.DataFrame, epn_df: pd.DataFrame) -> Dict:
        """Benchmark the join_epn_transactions method."""
        print("🔍 Benchmarking join_epn_transactions...")
        
        # Warm up
        _ = self.processor.join_epn_transactions(combined_df.head(10), epn_df.head(10))
        
        # Benchmark
        start_time = time.time()
        result = self.processor.join_epn_transactions(combined_df, epn_df)
        end_time = time.time()
        
        execution_time = end_time - start_time
        rows_processed = len(combined_df)
        
        return {
            'method': 'join_epn_transactions',
            'execution_time': execution_time,
            'rows_input': rows_processed,
            'rows_output': len(result),
            'rows_per_second': rows_processed / execution_time if execution_time > 0 else 0
        }
    
    def benchmark_calculate_time_intervals(self, df: pd.DataFrame) -> Dict:
        """Benchmark the calculate_time_intervals method."""
        print("🔍 Benchmarking calculate_time_intervals...")
        
        # Warm up
        _ = self.processor.calculate_time_intervals(df.head(10))
        
        # Benchmark
        start_time = time.time()
        result = self.processor.calculate_time_intervals(df)
        end_time = time.time()
        
        execution_time = end_time - start_time
        rows_processed = len(df)
        
        return {
            'method': 'calculate_time_intervals',
            'execution_time': execution_time,
            'rows_input': rows_processed,
            'rows_output': len(result),
            'rows_per_second': rows_processed / execution_time if execution_time > 0 else 0
        }
    
    def run_full_benchmark(self, data_sizes: List[int] = [100, 500, 1000, 2000, 5000]) -> pd.DataFrame:
        """Run comprehensive benchmark across different data sizes."""
        print("🚀 Running comprehensive performance benchmark...")
        
        benchmark_results = []
        
        for size in data_sizes:
            print(f"\n📊 Testing with {size} rows...")
            
            # Generate test data
            test_data = self.generate_test_data(size)
            
            # Benchmark unnest_pushed_to_users
            result1 = self.benchmark_unnest_pushed_to_users(test_data['server_items'])
            result1['data_size'] = size
            benchmark_results.append(result1)
            
            # Prepare data for subsequent benchmarks
            expanded_server = self.processor.unnest_pushed_to_users(test_data['server_items'], '0722-725C')
            if not expanded_server.empty:
                # Add required columns for time calculations
                expanded_server['action_time'] = pd.date_range('2025-01-01', periods=len(expanded_server), freq='1H')
                
                # Benchmark join_epn_transactions
                result2 = self.benchmark_join_epn_transactions(expanded_server, test_data['epn_transactions'])
                result2['data_size'] = size
                benchmark_results.append(result2)
                
                # Benchmark calculate_time_intervals
                result3 = self.benchmark_calculate_time_intervals(expanded_server)
                result3['data_size'] = size
                benchmark_results.append(result3)
        
        results_df = pd.DataFrame(benchmark_results)
        return results_df
    
    def generate_performance_report(self, results_df: pd.DataFrame) -> None:
        """Generate a comprehensive performance report."""
        print("\n📈 Generating performance report...")
        
        # Save raw results
        results_df.to_csv('performance_benchmark_results.csv', index=False)
        print("💾 Raw results saved to: performance_benchmark_results.csv")
        
        # Print summary statistics
        print("\n📊 Performance Summary:")
        print("=" * 60)
        
        for method in results_df['method'].unique():
            method_data = results_df[results_df['method'] == method]
            print(f"\n{method}:")
            print(f"  Average execution time: {method_data['execution_time'].mean():.4f}s")
            print(f"  Average throughput: {method_data['rows_per_second'].mean():.0f} rows/sec")
            print(f"  Best throughput: {method_data['rows_per_second'].max():.0f} rows/sec")
            print(f"  Worst throughput: {method_data['rows_per_second'].min():.0f} rows/sec")
        
        # Create performance visualization
        try:
            plt.figure(figsize=(15, 10))
            
            # Execution time by data size
            plt.subplot(2, 2, 1)
            for method in results_df['method'].unique():
                method_data = results_df[results_df['method'] == method]
                plt.plot(method_data['data_size'], method_data['execution_time'], 
                        marker='o', label=method)
            plt.xlabel('Data Size (rows)')
            plt.ylabel('Execution Time (seconds)')
            plt.title('Execution Time vs Data Size')
            plt.legend()
            plt.grid(True)
            
            # Throughput by data size
            plt.subplot(2, 2, 2)
            for method in results_df['method'].unique():
                method_data = results_df[results_df['method'] == method]
                plt.plot(method_data['data_size'], method_data['rows_per_second'], 
                        marker='s', label=method)
            plt.xlabel('Data Size (rows)')
            plt.ylabel('Throughput (rows/second)')
            plt.title('Throughput vs Data Size')
            plt.legend()
            plt.grid(True)
            
            # Performance comparison bar chart
            plt.subplot(2, 2, 3)
            avg_throughput = results_df.groupby('method')['rows_per_second'].mean()
            avg_throughput.plot(kind='bar')
            plt.title('Average Throughput by Method')
            plt.ylabel('Rows/Second')
            plt.xticks(rotation=45)
            
            # Execution time distribution
            plt.subplot(2, 2, 4)
            results_df.boxplot(column='execution_time', by='method', ax=plt.gca())
            plt.title('Execution Time Distribution')
            plt.suptitle('')  # Remove default title
            
            plt.tight_layout()
            plt.savefig('performance_benchmark_charts.png', dpi=300, bbox_inches='tight')
            print("📊 Performance charts saved to: performance_benchmark_charts.png")
            
        except ImportError:
            print("⚠️ Matplotlib not available - skipping chart generation")


def main():
    """Main benchmark execution."""
    print("🔬 Data Processing Performance Benchmark")
    print("=" * 50)
    
    benchmark = PerformanceBenchmark()
    
    # Run benchmarks
    results = benchmark.run_full_benchmark()
    
    # Generate report
    benchmark.generate_performance_report(results)
    
    print("\n✅ Benchmark completed successfully!")
    print("📁 Check performance_benchmark_results.csv for detailed results")
    print("📊 Check performance_benchmark_charts.png for visualizations")


if __name__ == "__main__":
    main()
