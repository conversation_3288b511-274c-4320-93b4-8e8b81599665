#!/usr/bin/env python3
"""
Data Processor - Handles data transformation and analysis logic
OPTIMIZED: Added Numba JIT compilation for mathematical operations
"""

import pandas as pd
import numpy as np

# Try to import numba for JIT compilation, fall back gracefully if not available
try:
    from numba import jit
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    # Create a no-op decorator if numba is not available
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator


@jit(nopython=True, cache=True)
def calculate_time_intervals_numba(action_time_seconds, start_time_seconds, end_time_seconds, found_time):
    """
    JIT-compiled function for fast time interval calculations.
    Returns (time_clicked, time_sold) as tuple.
    """
    if np.isnan(action_time_seconds):
        return np.nan, np.nan

    time_clicked = action_time_seconds - start_time_seconds - found_time
    time_sold = end_time_seconds - action_time_seconds

    return time_clicked, time_sold


class DataProcessor:
    def __init__(self):
        self.numba_available = NUMBA_AVAILABLE
        if self.numba_available:
            print("✓ Numba JIT compilation enabled for mathematical operations")
        else:
            print("ℹ️ Numba not available - using standard Python operations")

    def unnest_pushed_to_users(self, df: pd.DataFrame, license_prefix: str) -> pd.DataFrame:
        """
        Replicate SQL's CROSS JOIN LATERAL unnest(srv.pushed_to_users) functionality.
        Expands each row for each license key in pushed_to_users array.
        OPTIMIZED: Uses vectorized operations instead of iterrows() for better performance.
        """
        if df.empty:
            return pd.DataFrame()

        # Create a copy to avoid modifying the original
        df_work = df.copy()

        # Vectorized string parsing for pushed_to_users
        def parse_pushed_users(pushed_users):
            if not pushed_users:
                return []
            if isinstance(pushed_users, str):
                try:
                    import ast
                    return ast.literal_eval(pushed_users)
                except (ValueError, SyntaxError):
                    return []
            if isinstance(pushed_users, list):
                return pushed_users
            return []

        # Apply parsing vectorized
        df_work['parsed_users'] = df_work['pushed_to_users'].apply(parse_pushed_users)

        # Filter out rows with empty parsed_users
        df_work = df_work[df_work['parsed_users'].apply(len) > 0]

        if df_work.empty:
            return pd.DataFrame()

        # Use explode to unnest the arrays - this is much faster than iterrows
        df_exploded = df_work.explode('parsed_users')

        # Filter by license prefix vectorized
        mask = (df_exploded['parsed_users'].notna() &
                df_exploded['parsed_users'].str.startswith(license_prefix))
        result_df = df_exploded[mask].copy()

        if result_df.empty:
            return pd.DataFrame()

        # Rename the exploded column to license_key
        result_df['license_key'] = result_df['parsed_users']

        # Drop the temporary columns
        result_df = result_df.drop(['pushed_to_users', 'parsed_users'], axis=1)

        print(f"✓ Expanded {len(df)} server items to {len(result_df)} license key combinations (vectorized)")
        return result_df

    def join_clicks_and_purchases(self, server_df: pd.DataFrame, clicks_df: pd.DataFrame) -> pd.DataFrame:
        """
        Join server items with clicks_and_purchases data.
        Replicates the LEFT JOIN logic from the SQL.
        """
        if server_df.empty:
            return pd.DataFrame()

        if clicks_df.empty:
            # No clicks data - return server data with null action_time and action_type
            result_df = server_df.copy()
            result_df['action_time'] = pd.NaT
            result_df['action_type'] = None
            print("ℹ️ No clicks/purchases data found - proceeding with server data only")
            return result_df

        # Perform left join on item_id and license_key
        result_df = server_df.merge(
            clicks_df[['item_id', 'license_key', 'action_time', 'action_type']],
            on=['item_id', 'license_key'],
            how='left'
        )

        print(f"✓ Joined {len(server_df)} server items with clicks/purchases data")
        return result_df

    def join_sold_items(self, combined_df: pd.DataFrame, sold_df: pd.DataFrame) -> pd.DataFrame:
        """
        Join combined data with sold_items data.
        Replicates the INNER JOIN logic from the SQL.
        """
        if combined_df.empty or sold_df.empty:
            print("ℹ️ No data to join with sold_items")
            return pd.DataFrame()

        # Perform inner join on item_id
        result_df = combined_df.merge(
            sold_df[['item_id', 'buyer_name', 'buyer_feedback_count', 'leaf_category_id', 'price', 'quantity_sold', 'duration', 'end_time']],
            on='item_id',
            how='inner'
        )

        print(f"✓ Joined with sold_items: {len(combined_df)} → {len(result_df)} items")
        return result_df

    def join_epn_transactions(self, combined_df: pd.DataFrame, epn_df: pd.DataFrame) -> pd.DataFrame:
        """
        Join combined data with epn_transactions data.
        Replicates the LEFT JOIN with partial license key matching from the SQL.
        Adds 'credited' status based on whether EPN transaction exists.
        """
        if combined_df.empty:
            return pd.DataFrame()

        if epn_df.empty:
            print("ℹ️ No EPN transactions data found - all items marked as not credited")
            combined_df = combined_df.copy()
            combined_df['credited'] = 'No'
            combined_df['eventdate'] = pd.NaT
            return combined_df

        # Add partial license key column to combined_df (first 9 characters)
        combined_df = combined_df.copy()
        combined_df['partial_license_key'] = combined_df['license_key'].str[:9]

        # Rename columns in epn_df to match expected names
        epn_df = epn_df.rename(columns={'itemid': 'item_id'})

        # Perform left join on item_id and partial license key
        result_df = combined_df.merge(
            epn_df[['item_id', 'customid_itemid', 'customid_partial_license_key', 'eventdate']],
            left_on=['item_id', 'partial_license_key'],
            right_on=['item_id', 'customid_partial_license_key'],
            how='left'
        )

        # Add credited status: 'Yes' if we have EPN transaction, 'No' if not
        result_df['credited'] = result_df['customid_partial_license_key'].notna().map({True: 'Yes', False: 'No'})

        # Add item_id_match column: True if item_id matches customid_itemid, False otherwise
        # OPTIMIZED: Vectorized comparison instead of apply() for better performance
        item_id_numeric = pd.to_numeric(result_df['item_id'], errors='coerce')
        customid_numeric = pd.to_numeric(result_df['customid_itemid'], errors='coerce')

        # Vectorized comparison - much faster than apply()
        result_df['item_id_match'] = (
            item_id_numeric.notna() &
            customid_numeric.notna() &
            (item_id_numeric == customid_numeric)
        )

        # Clean up temporary columns
        result_df = result_df.drop(['partial_license_key', 'customid_partial_license_key'], axis=1, errors='ignore')

        credited_count = (result_df['credited'] == 'Yes').sum()
        not_credited_count = (result_df['credited'] == 'No').sum()
        print(f"✓ Joined with EPN transactions data: {credited_count} credited, {not_credited_count} not credited")
        return result_df

    def add_action_ranking(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add action ranking to replicate SQL's ROW_NUMBER() OVER functionality.
        Ranks actions by time for each item_id and license_key combination.
        """
        if df.empty:
            return df

        df = df.copy()

        # Ensure action_time is properly converted to datetime
        df['action_time'] = pd.to_datetime(df['action_time'], errors='coerce')

        # Handle null action_time values by assigning them a very late timestamp for ranking
        # This ensures they get ranked last, but still get rank 1 if they're the only record
        df['action_time_for_ranking'] = df['action_time'].fillna(pd.Timestamp('2099-12-31'))

        # Add ranking: ROW_NUMBER() OVER(PARTITION BY item_id, license_key ORDER BY action_time ASC)
        df['action_rank'] = df.groupby(['item_id', 'license_key'])['action_time_for_ranking'].rank(
            method='first', ascending=True
        ).astype(int)

        # Remove the temporary ranking column
        df = df.drop('action_time_for_ranking', axis=1)

        print(f"✓ Added action ranking to {len(df)} records")
        return df

    def calculate_time_intervals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate time intervals matching the SQL logic:
        - time_clicked: COALESCE(EXTRACT(EPOCH FROM (action_time - start_datetime)), 0) - found_time
        - time_sold: COALESCE(EXTRACT(EPOCH FROM (end_time - action_time)), 0)
        OPTIMIZED: Uses Numba JIT compilation when available for faster calculations.
        """
        if df.empty:
            return df

        df = df.copy()

        # Convert datetime columns to pandas datetime if they aren't already
        datetime_cols = ['start_datetime', 'action_time', 'end_time']
        for col in datetime_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col])

        if self.numba_available and len(df) > 1000:  # Use JIT for larger datasets
            # Convert to seconds for JIT function
            action_time_seconds = df['action_time'].astype('int64') / 1e9
            start_time_seconds = df['start_datetime'].astype('int64') / 1e9
            end_time_seconds = df['end_time'].astype('int64') / 1e9
            found_time = df['found_time'].fillna(0).values

            # Use JIT-compiled function for vectorized calculations
            results = np.array([
                calculate_time_intervals_numba(
                    action_time_seconds.iloc[i] if not pd.isna(action_time_seconds.iloc[i]) else np.nan,
                    start_time_seconds.iloc[i] if not pd.isna(start_time_seconds.iloc[i]) else np.nan,
                    end_time_seconds.iloc[i] if not pd.isna(end_time_seconds.iloc[i]) else np.nan,
                    found_time[i]
                ) for i in range(len(df))
            ])

            df['time_clicked'] = results[:, 0]
            df['time_sold'] = results[:, 1]
        else:
            # Fallback to standard pandas operations
            time_diff_seconds = (df['action_time'] - df['start_datetime']).dt.total_seconds()
            df['time_clicked'] = time_diff_seconds - df['found_time'].fillna(0)
            df.loc[df['action_time'].isna(), 'time_clicked'] = None

            time_sold_seconds = (df['end_time'] - df['action_time']).dt.total_seconds()
            df['time_sold'] = time_sold_seconds
            df.loc[df['action_time'].isna(), 'time_sold'] = None

        # Add found_and_sent_datetime column (same as start_datetime)
        df['found_and_sent_datetime'] = df['start_datetime']

        # Rename columns
        df = df.rename(columns={
            'found_time': 'time_found_and_sent',
            'action_time': 'click_datetime',
            'end_time': 'sold_datetime'
        })

        # Aggregate action types for each item_id and license_key combination
        df = self.aggregate_action_types(df)

        print(f"✓ Calculated time intervals for {len(df)} records")
        return df

    def aggregate_action_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Aggregate action_type values for each item_id and license_key combination.
        Multiple action types are joined with semicolons.
        """
        if df.empty:
            df['action_types'] = ''
            return df

        if 'action_type' not in df.columns:
            df['action_types'] = ''
            return df

        # Group by item_id and license_key, then aggregate action_type values
        # OPTIMIZED: Use agg() with custom function for better performance
        def join_unique_sorted(series):
            return ';'.join(sorted(set(series.dropna().astype(str))))

        action_type_agg = df.groupby(['item_id', 'license_key'])['action_type'].agg(join_unique_sorted).reset_index()
        action_type_agg = action_type_agg.rename(columns={'action_type': 'action_types'})

        # Merge back with the main dataframe
        df = df.merge(action_type_agg, on=['item_id', 'license_key'], how='left')

        # Fill empty action_types with empty string
        df['action_types'] = df['action_types'].fillna('')
        return df

    def apply_final_filtering_and_sorting(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply final filtering and sorting to match SQL logic:
        - Filter for action_rank = 1 (earliest action for each item/license combination)
        - Sort by start_datetime DESC
        """
        if df.empty:
            return df

        # Filter for earliest actions only (action_rank = 1)
        filtered_df = df[df['action_rank'] == 1].copy()

        # Sort by start_datetime DESC
        result_df = filtered_df.sort_values('start_datetime', ascending=False)

        # Select final columns to match SQL output
        final_columns = [
            'item_id', 'customid_itemid', 'item_id_match', 'license_key', 'buyer_name', 'buyer_feedback_count', 'leaf_category_id', 'price', 'quantity_sold',
            'credited', 'action_types', 'time_found_and_sent', 'time_clicked', 'time_sold', 'duration',
            'found_and_sent_datetime', 'click_datetime', 'sold_datetime'
        ]

        # Only include columns that exist in the dataframe
        available_columns = [col for col in final_columns if col in result_df.columns]
        result_df = result_df[available_columns]

        print(f"✓ Applied final filtering: {len(df)} → {len(result_df)} records")
        return result_df
