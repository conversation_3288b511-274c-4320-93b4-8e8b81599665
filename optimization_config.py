#!/usr/bin/env python3
"""
Optimization Configuration
Centralized settings for performance optimization features.
"""

import os
from multiprocessing import cpu_count


class OptimizationConfig:
    """Configuration class for optimization settings."""
    
    # Numba JIT compilation settings
    ENABLE_NUMBA = True
    NUMBA_MIN_ROWS_THRESHOLD = 1000  # Use JIT only for datasets larger than this
    
    # Multiprocessing settings
    ENABLE_PARALLEL_PROCESSING = True
    DEFAULT_MAX_WORKERS = min(cpu_count(), 8)  # Limit to 8 workers max
    MIN_ITEMS_FOR_PARALLEL = 2  # Minimum items needed to enable parallel processing
    
    # Vectorization settings
    ENABLE_VECTORIZATION = True
    CHUNK_SIZE = 10000  # Process data in chunks for memory efficiency
    
    # Performance monitoring
    ENABLE_PERFORMANCE_LOGGING = True
    LOG_SLOW_OPERATIONS_THRESHOLD = 5.0  # Log operations taking longer than 5 seconds
    
    # Cache settings for optimization
    ENABLE_RESULT_CACHING = True
    OPTIMIZATION_CACHE_DIR = "optimization_cache"
    
    @classmethod
    def get_max_workers(cls, num_items: int = None) -> int:
        """
        Get the optimal number of workers based on available CPUs and workload.
        
        Args:
            num_items: Number of items to process (optional)
            
        Returns:
            Optimal number of workers
        """
        if not cls.ENABLE_PARALLEL_PROCESSING:
            return 1
            
        max_workers = cls.DEFAULT_MAX_WORKERS
        
        if num_items:
            # Don't use more workers than items
            max_workers = min(max_workers, num_items)
            
            # For small workloads, use fewer workers to reduce overhead
            if num_items < 10:
                max_workers = min(max_workers, 2)
        
        return max_workers
    
    @classmethod
    def should_use_numba(cls, data_size: int) -> bool:
        """
        Determine if Numba JIT compilation should be used based on data size.
        
        Args:
            data_size: Number of rows in the dataset
            
        Returns:
            True if Numba should be used, False otherwise
        """
        return (cls.ENABLE_NUMBA and 
                data_size >= cls.NUMBA_MIN_ROWS_THRESHOLD)
    
    @classmethod
    def should_use_parallel(cls, num_items: int) -> bool:
        """
        Determine if parallel processing should be used.
        
        Args:
            num_items: Number of items to process
            
        Returns:
            True if parallel processing should be used, False otherwise
        """
        return (cls.ENABLE_PARALLEL_PROCESSING and 
                num_items >= cls.MIN_ITEMS_FOR_PARALLEL)
    
    @classmethod
    def get_chunk_size(cls, total_size: int) -> int:
        """
        Get optimal chunk size for processing large datasets.
        
        Args:
            total_size: Total number of rows to process
            
        Returns:
            Optimal chunk size
        """
        if total_size <= cls.CHUNK_SIZE:
            return total_size
        
        # Use smaller chunks for very large datasets
        if total_size > 100000:
            return cls.CHUNK_SIZE // 2
        
        return cls.CHUNK_SIZE
    
    @classmethod
    def create_cache_dir(cls) -> str:
        """
        Create and return the optimization cache directory path.
        
        Returns:
            Path to the cache directory
        """
        if cls.ENABLE_RESULT_CACHING:
            os.makedirs(cls.OPTIMIZATION_CACHE_DIR, exist_ok=True)
            return cls.OPTIMIZATION_CACHE_DIR
        return None


# Global configuration instance
config = OptimizationConfig()


def print_optimization_status():
    """Print the current optimization configuration status."""
    print("🔧 Optimization Configuration Status:")
    print("=" * 40)
    print(f"Numba JIT compilation: {'✓ Enabled' if config.ENABLE_NUMBA else '✗ Disabled'}")
    print(f"Parallel processing: {'✓ Enabled' if config.ENABLE_PARALLEL_PROCESSING else '✗ Disabled'}")
    print(f"Vectorization: {'✓ Enabled' if config.ENABLE_VECTORIZATION else '✗ Disabled'}")
    print(f"Performance logging: {'✓ Enabled' if config.ENABLE_PERFORMANCE_LOGGING else '✗ Disabled'}")
    print(f"Result caching: {'✓ Enabled' if config.ENABLE_RESULT_CACHING else '✗ Disabled'}")
    print(f"Max workers: {config.DEFAULT_MAX_WORKERS}")
    print(f"Numba threshold: {config.NUMBA_MIN_ROWS_THRESHOLD} rows")
    print(f"Chunk size: {config.CHUNK_SIZE} rows")


if __name__ == "__main__":
    print_optimization_status()
