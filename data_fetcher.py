#!/usr/bin/env python3
"""
Data Fetcher - Handles database connections and data retrieval with caching
"""

import sys
from typing import Dict

import pandas as pd
import psycopg2
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine

from cache_manager import CacheManager

# Import database config
try:
    from config import db_config
except ImportError:
    print("Error: config.py not found. Please ensure database configuration exists.")
    sys.exit(1)


class DataFetcher:
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.engine = None

    def connect_db(self) -> Engine:
        """Establish database connection using SQLAlchemy engine."""
        if self.engine is None:
            try:
                # Create SQLAlchemy connection string from psycopg2 config
                connection_string = (
                    f"postgresql://{db_config['user']}:{db_config['password']}"
                    f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
                )
                self.engine = create_engine(connection_string)
                print("✓ Database connection established")
            except Exception as e:
                print(f"✗ Database connection failed: {e}")
                sys.exit(1)
        return self.engine

    def fetch_server_items(self, license_prefix: str, start_date: str, end_date: str,
                          max_cache_age: int = 24) -> pd.DataFrame:
        """Fetch server_items data with caching."""
        params = {
            'license_prefix': license_prefix,
            'start_date': start_date,
            'end_date': end_date
        }
        cache_path = self.cache_manager.get_cache_path('server_items', params)

        # Try to load from cache first
        if self.cache_manager.is_cache_valid(cache_path, max_cache_age):
            cached_data = self.cache_manager.load_from_cache(cache_path)
            if cached_data is not None:
                return cached_data

        # Fetch from database
        print("📡 Fetching server_items data from database...")
        engine = self.connect_db()

        query = """
        SELECT
            srv.item_id,
            srv.found_time,
            srv.start_datetime,
            srv.pushed_to_users
        FROM server_items srv
        WHERE srv.start_datetime >= %(start_date)s
            AND srv.start_datetime < %(end_date)s
            AND srv.pushed_to_users IS NOT NULL
            AND EXISTS (
                SELECT 1 FROM unnest(srv.pushed_to_users) AS license_key
                WHERE license_key LIKE %(license_pattern)s
            )
        """

        try:
            # Add % wildcard for LIKE query
            license_pattern = f"{license_prefix}%"
            df = pd.read_sql_query(query, engine, params={
                'start_date': start_date,
                'end_date': end_date,
                'license_pattern': license_pattern
            })
            self.cache_manager.save_to_cache(df, cache_path)
            return df
        except Exception as e:
            print(f"✗ Error fetching server_items: {e}")
            return pd.DataFrame()

    def fetch_clicks_and_purchases(self, license_prefix: str, start_date: str, end_date: str,
                                  max_cache_age: int = 24) -> pd.DataFrame:
        """Fetch clicks_and_purchases data with caching."""
        params = {
            'license_prefix': license_prefix,
            'start_date': start_date,
            'end_date': end_date
        }
        cache_path = self.cache_manager.get_cache_path('clicks_and_purchases', params)

        # Try to load from cache first
        if self.cache_manager.is_cache_valid(cache_path, max_cache_age):
            cached_data = self.cache_manager.load_from_cache(cache_path)
            if cached_data is not None:
                return cached_data

        # Fetch from database
        print("📡 Fetching clicks_and_purchases data from database...")
        engine = self.connect_db()

        query = """
        SELECT
            cp.item_id,
            cp.license_key,
            cp.action_time,
            cp.action_type
        FROM clicks_and_purchases cp
        WHERE cp.action_time >= %(start_date)s
            AND cp.action_time < %(end_date)s
            AND cp.license_key IS NOT NULL
            AND cp.license_key LIKE %(license_pattern)s
        """

        try:
            # Add % wildcard for LIKE query
            license_pattern = f"{license_prefix}%"
            df = pd.read_sql_query(query, engine, params={
                'start_date': start_date,
                'end_date': end_date,
                'license_pattern': license_pattern
            })
            self.cache_manager.save_to_cache(df, cache_path)
            return df
        except Exception as e:
            print(f"✗ Error fetching clicks_and_purchases: {e}")
            return pd.DataFrame()

    def fetch_sold_items(self, buyer_names: str, start_date: str, end_date: str,
                        max_cache_age: int = 24) -> pd.DataFrame:
        """Fetch sold_items data with caching."""
        # Parse comma-separated buyer names
        buyer_list = [name.strip() for name in buyer_names.split(',')]

        params = {
            'buyer_names': buyer_names,
            'start_date': start_date,
            'end_date': end_date
        }
        cache_path = self.cache_manager.get_cache_path('sold_items', params)

        # Try to load from cache first
        if self.cache_manager.is_cache_valid(cache_path, max_cache_age):
            cached_data = self.cache_manager.load_from_cache(cache_path)
            if cached_data is not None:
                return cached_data

        # Fetch from database
        print("📡 Fetching sold_items data from database...")
        engine = self.connect_db()

        # Build IN clause for multiple buyer names using named parameters
        buyer_params = {}
        buyer_placeholders = []
        for i, buyer in enumerate(buyer_list):
            param_name = f'buyer_{i}'
            buyer_params[param_name] = buyer
            buyer_placeholders.append(f'%({param_name})s')

        placeholders = ','.join(buyer_placeholders)
        query = f"""
        SELECT
            si.item_id,
            si.buyer_name,
            si.buyer_feedback_count,
            si.leaf_category_id,
            si.price,
            si.quantity_sold,
            si.duration,
            si.end_time
        FROM sold_items si
        WHERE si.buyer_name IN ({placeholders})
            AND si.end_time >= %(start_date)s
            AND si.end_time < %(end_date)s
        """

        try:
            # Combine buyer parameters with date parameters
            query_params = {
                **buyer_params,
                'start_date': start_date,
                'end_date': end_date
            }
            df = pd.read_sql_query(query, engine, params=query_params)
            self.cache_manager.save_to_cache(df, cache_path)
            return df
        except Exception as e:
            print(f"✗ Error fetching sold_items: {e}")
            return pd.DataFrame()

    def fetch_epn_transactions(self, start_date: str, end_date: str,
                              max_cache_age: int = 24) -> pd.DataFrame:
        """Fetch epn_transactions data with caching."""
        params = {
            'start_date': start_date,
            'end_date': end_date
        }
        cache_path = self.cache_manager.get_cache_path('epn_transactions', params)

        # Try to load from cache first
        if self.cache_manager.is_cache_valid(cache_path, max_cache_age):
            cached_data = self.cache_manager.load_from_cache(cache_path)
            if cached_data is not None:
                return cached_data

        # Fetch from database
        print("📡 Fetching epn_transactions data from database...")
        engine = self.connect_db()

        query = """
        SELECT
            et.itemid,
            et.customid_itemid,
            et.customid_partial_license_key,
            et.eventdate
        FROM epn_transactions et
        WHERE et.eventdate >= %(start_date)s
            AND et.eventdate < %(end_date)s
            AND et.customid_partial_license_key IS NOT NULL
        """

        try:
            df = pd.read_sql_query(query, engine, params={
                'start_date': start_date,
                'end_date': end_date
            })
            self.cache_manager.save_to_cache(df, cache_path)
            return df
        except Exception as e:
            print(f"✗ Error fetching epn_transactions: {e}")
            return pd.DataFrame()

    def close_connection(self):
        """Close database connection."""
        if self.engine:
            self.engine.dispose()
            print("🔌 Database connection closed")
