# Item Journey Analyzer

Python script that replicates the functionality of `item_journey_by_license_key.sql` with caching for faster iteration.

## Installation

```bash
pip install -r requirements.txt
```

## Usage

### Basic usage (matches SQL defaults):
```bash
python item_journey_analyzer.py
```

### Custom parameters:
```bash
python item_journey_analyzer.py --license-prefix "4B4F" --buyer-name "john" --start-date "2025-05-01" --end-date "2025-06-01"
```

### Clear cache and save results:
```bash
python item_journey_analyzer.py --clear-cache --output results.csv
```

### Command-line options:
- `--license-prefix`: License key prefix (default: "3A3E")
- `--buyer-name`: Buyer name filter (default: "le")  
- `--start-date`: Start date YYYY-MM-DD (default: "2025-06-01")
- `--end-date`: End date YYYY-MM-DD (default: "2025-07-01")
- `--cache-dir`: Cache directory (default: "cache")
- `--clear-cache`: Clear cached data before processing
- `--output`: Save results to CSV file
- `--max-cache-age`: Maximum cache age in hours (default: 24)

## Features

- **Caching**: Pre-downloads and caches database queries for faster iteration
- **Progress indicators**: Shows progress during data processing
- **Flexible parameters**: Command-line options for all key filters
- **Error handling**: Comprehensive error checking and user feedback
- **Output options**: Console display and CSV export