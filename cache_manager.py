#!/usr/bin/env python3
"""
Cache Manager - Handles CSV file caching for faster data iteration
"""

from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional

import pandas as pd


class CacheManager:
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def get_cache_path(self, table_name: str, params: Dict) -> Path:
        """Generate cache file path based on table and parameters."""
        param_str = "_".join(f"{k}-{v}" for k, v in sorted(params.items()))
        return self.cache_dir / f"{table_name}_{param_str}.csv"
    
    def is_cache_valid(self, cache_path: Path, max_age_hours: int = 24) -> bool:
        """Check if cache file exists and is within age limit."""
        if not cache_path.exists():
            return False
        
        age = datetime.now() - datetime.fromtimestamp(cache_path.stat().st_mtime)
        return age < timedelta(hours=max_age_hours)
    
    def load_from_cache(self, cache_path: Path) -> Optional[pd.DataFrame]:
        """Load data from cache file."""
        try:
            data = pd.read_csv(cache_path)
            print(f"✓ Loaded cached data from {cache_path.name}")
            return data
        except (FileNotFoundError, pd.errors.EmptyDataError, pd.errors.ParserError) as e:
            print(f"✗ Failed to load cache {cache_path.name}: {e}")
            return None
    
    def save_to_cache(self, data: pd.DataFrame, cache_path: Path) -> bool:
        """Save data to cache file."""
        try:
            data.to_csv(cache_path, index=False)
            print(f"✓ Saved data to cache {cache_path.name}")
            return True
        except (IOError, OSError) as e:
            print(f"✗ Failed to save cache {cache_path.name}: {e}")
            return False
    
    def clear_cache(self) -> None:
        """Clear all cached data files."""
        cache_files = list(self.cache_dir.glob("*.csv"))
        if not cache_files:
            print("ℹ️ No cache files found to clear")
            return
        
        cleared_count = 0
        for cache_file in cache_files:
            try:
                cache_file.unlink()
                cleared_count += 1
            except OSError as e:
                print(f"✗ Failed to delete {cache_file.name}: {e}")
        
        print(f"✓ Cleared {cleared_count} cache files")
    
    def get_cache_info(self) -> Dict[str, Dict]:
        """Get information about cached files."""
        cache_files = list(self.cache_dir.glob("*.csv"))
        cache_info = {}
        
        for cache_file in cache_files:
            try:
                stat = cache_file.stat()
                age_hours = (datetime.now() - datetime.fromtimestamp(stat.st_mtime)).total_seconds() / 3600
                size_mb = stat.st_size / (1024 * 1024)
                
                cache_info[cache_file.name] = {
                    'age_hours': round(age_hours, 1),
                    'size_mb': round(size_mb, 2),
                    'valid': age_hours < 24
                }
            except OSError:
                continue
        
        return cache_info
