#!/usr/bin/env python3
"""
Batch Item Journey Analyzer
Reads license keys and buyer names from input file and processes each license key.
OPTIMIZED: Added multiprocessing support for parallel license key processing.
"""

import os
import sys
import subprocess
import argparse
import pandas as pd
from collections import defaultdict
from datetime import datetime
from stats_tracker import StatsTracker
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import cpu_count
import time


def read_input_file(file_path):
    """
    Read the input file and parse license keys and buyer names.
    Returns a dictionary with license keys as keys and comma-separated buyer names as values.
    """
    license_buyers = defaultdict(list)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                # Try tab separator first, then comma
                if '\t' in line:
                    parts = line.split('\t')
                elif ',' in line:
                    parts = line.split(',')
                else:
                    print(f"⚠️ Warning: Line {line_num} doesn't contain tab or comma separator: {line}")
                    continue

                if len(parts) < 2:
                    print(f"⚠️ Warning: Line {line_num} doesn't have enough columns: {line}")
                    continue

                license_key = parts[0].strip()
                buyer_name = parts[1].strip()

                if license_key and buyer_name:
                    license_buyers[license_key].append(buyer_name)
                else:
                    print(f"⚠️ Warning: Line {line_num} has empty license key or buyer name: {line}")

    except FileNotFoundError:
        print(f"❌ Error: Input file '{file_path}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error reading input file: {e}")
        sys.exit(1)

    # Combine buyer names for each license key
    result = {}
    for license_key, buyers in license_buyers.items():
        # Remove duplicates while preserving order
        unique_buyers = list(dict.fromkeys(buyers))
        result[license_key] = ','.join(unique_buyers)

    return result


def save_results_to_file(results_df, license_prefix, buyer_names, start_date, end_date):
    """
    Save results DataFrame to CSV file in results_per_key folder.
    """
    results_folder = "results_per_key"
    os.makedirs(results_folder, exist_ok=True)

    if not results_df.empty:
        output_file = os.path.join(results_folder, f"item_journey_results_{license_prefix}_{start_date}_to_{end_date}.csv")
        results_df.to_csv(output_file, index=False)
        print(f"💾 Results saved to: {output_file}")
    else:
        print("ℹ️ No results to save (empty DataFrame)")


def run_analyzer_worker(args_tuple):
    """
    Worker function for parallel processing.
    Takes a tuple of arguments to work with multiprocessing.
    Returns tuple: (license_prefix, buyer_names, success: bool, results_df: pd.DataFrame, stats: dict)
    """
    license_prefix, buyer_names, start_date, end_date, additional_args = args_tuple

    try:
        # Import and run the analyzer directly
        from main_analyzer import ItemJourneyAnalyzer
        from stats_tracker import StatsTracker

        # Parse additional arguments
        clear_cache = '--clear-cache' in (additional_args or [])
        max_cache_age = 24
        if additional_args:
            for arg in additional_args:
                if arg.startswith('--max-cache-age='):
                    max_cache_age = int(arg.split('=')[1])

        # Create analyzer and run analysis
        analyzer = ItemJourneyAnalyzer()
        results_df = analyzer.analyze_item_journey(
            license_prefix=license_prefix,
            buyer_names=buyer_names,
            start_date=start_date,
            end_date=end_date,
            max_cache_age=max_cache_age,
            clear_cache=clear_cache
        )

        # Calculate statistics
        stats_tracker = StatsTracker()
        stats = stats_tracker.calculate_stats(results_df, license_prefix, buyer_names)

        # Close database connection
        analyzer.close_connection()

        return license_prefix, buyer_names, True, results_df, stats

    except Exception as e:
        print(f"❌ Error processing {license_prefix}: {e}")
        return license_prefix, buyer_names, False, pd.DataFrame(), {}


def run_analyzer(license_prefix, buyer_names, start_date, end_date, additional_args=None):
    """
    Run the main analyzer for a specific license key and buyer names.
    Returns tuple: (success: bool, results_df: pd.DataFrame)
    """
    print(f"\n🚀 Running analysis for license prefix '{license_prefix}' with buyers '{buyer_names}'")
    print(f"📅 Date range: {start_date} to {end_date}")

    try:
        # Import and run the analyzer directly
        from main_analyzer import ItemJourneyAnalyzer

        # Parse additional arguments
        clear_cache = '--clear-cache' in (additional_args or [])
        max_cache_age = 24
        if additional_args:
            for arg in additional_args:
                if arg.startswith('--max-cache-age='):
                    max_cache_age = int(arg.split('=')[1])

        # Create analyzer and run analysis
        analyzer = ItemJourneyAnalyzer()
        results_df = analyzer.analyze_item_journey(
            license_prefix=license_prefix,
            buyer_names=buyer_names,
            start_date=start_date,
            end_date=end_date,
            max_cache_age=max_cache_age,
            clear_cache=clear_cache
        )

        # Save results to CSV file
        save_results_to_file(results_df, license_prefix, buyer_names, start_date, end_date)

        analyzer.close_connection()
        print(f"✅ Analysis completed successfully for {license_prefix}")
        return True, results_df

    except Exception as e:
        print(f"❌ Failed to run analysis for {license_prefix}: {e}")
        return False, pd.DataFrame()


def main():
    parser = argparse.ArgumentParser(
        description='Batch Item Journey Analyzer - Process multiple license keys from input file',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python batch_analyzer.py
  python batch_analyzer.py --input custom_input.txt --start-date 2025-06-01 --end-date 2025-07-01
  python batch_analyzer.py --clear-cache --max-cache-age 12
  python batch_analyzer.py --parallel --max-workers 4
  python batch_analyzer.py --parallel --limit 10
        """
    )

    parser.add_argument(
        '--input',
        default='input_license_key_buyer_names.txt',
        help='Input file with license keys and buyer names (default: input_license_key_buyer_names.txt)'
    )

    parser.add_argument(
        '--start-date',
        default='2025-06-01',
        help='Start date for analysis (default: 2025-06-01)'
    )

    parser.add_argument(
        '--end-date',
        default='2025-06-06',
        help='End date for analysis (default: 2025-06-06)'
    )

    parser.add_argument(
        '--clear-cache',
        action='store_true',
        help='Clear cache before each analysis'
    )

    parser.add_argument(
        '--max-cache-age',
        type=int,
        default=24,
        help='Maximum cache age in hours (default: 24)'
    )

    parser.add_argument(
        '--limit',
        type=int,
        help='Limit number of license keys to process (for testing)'
    )

    parser.add_argument(
        '--parallel',
        action='store_true',
        help='Enable parallel processing using multiple CPU cores'
    )

    parser.add_argument(
        '--max-workers',
        type=int,
        default=None,
        help=f'Maximum number of parallel workers (default: {cpu_count()} cores)'
    )

    args = parser.parse_args()

    print("🔄 Batch Item Journey Analyzer")
    print("=" * 50)

    # Read input file
    print(f"📖 Reading input file: {args.input}")
    license_buyers = read_input_file(args.input)

    print(f"✓ Found {len(license_buyers)} unique license keys")

    # Show summary of what will be processed
    print("\n📋 License keys to process:")
    for i, (license_key, buyers) in enumerate(license_buyers.items(), 1):
        buyer_count = len(buyers.split(','))
        print(f"  {i:3d}. {license_key} -> {buyer_count} buyer(s): {buyers}")
        if args.limit and i >= args.limit:
            print(f"  ... (limited to first {args.limit} keys)")
            break

    # Prepare additional arguments for main_analyzer.py
    additional_args = []
    if args.clear_cache:
        additional_args.append('--clear-cache')
    if args.max_cache_age != 24:
        additional_args.append(f'--max-cache-age={args.max_cache_age}')

    # Initialize statistics tracker (clear existing file for fresh start)
    stats_file = "stats_per_license.csv"
    if os.path.exists(stats_file):
        os.remove(stats_file)
        print(f"🗑️ Cleared existing stats file: {stats_file}")

    stats_tracker = StatsTracker(stats_file)

    # Process each license key
    items_to_process = list(license_buyers.items())
    if args.limit:
        items_to_process = items_to_process[:args.limit]

    successful = 0
    failed = 0

    if args.parallel and len(items_to_process) > 1:
        # Parallel processing
        max_workers = args.max_workers or min(cpu_count(), len(items_to_process))
        print(f"\n🚀 Starting parallel batch processing with {max_workers} workers...")
        print(f"📊 Processing {len(items_to_process)} license keys in parallel")

        # Prepare arguments for worker processes
        worker_args = [
            (license_key, buyers, args.start_date, args.end_date, additional_args)
            for license_key, buyers in items_to_process
        ]

        start_time = time.time()

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_license = {
                executor.submit(run_analyzer_worker, args): args[0]
                for args in worker_args
            }

            # Process completed tasks
            for i, future in enumerate(as_completed(future_to_license), 1):
                license_key = future_to_license[future]
                try:
                    license_prefix, buyer_names, success, results_df, stats = future.result()

                    print(f"\n✓ Completed {i}/{len(items_to_process)}: {license_prefix}")

                    if success:
                        successful += 1
                        # Save results and statistics
                        save_results_to_file(results_df, license_prefix, buyer_names, args.start_date, args.end_date)
                        stats_tracker.add_stats(stats)
                    else:
                        failed += 1

                except Exception as e:
                    print(f"❌ Error processing {license_key}: {e}")
                    failed += 1

        elapsed_time = time.time() - start_time
        print(f"\n⏱️ Parallel processing completed in {elapsed_time:.2f} seconds")

    else:
        # Sequential processing (original behavior)
        print(f"\n🚀 Starting sequential batch processing...")

        for i, (license_key, buyers) in enumerate(items_to_process, 1):
            print(f"\n{'='*60}")
            print(f"Processing {i}/{len(items_to_process)}: {license_key}")
            print(f"{'='*60}")

            success, results_df = run_analyzer(
                license_prefix=license_key,
                buyer_names=buyers,
                start_date=args.start_date,
                end_date=args.end_date,
                additional_args=additional_args
            )

            if success:
                successful += 1
                # Calculate and save statistics
                stats = stats_tracker.calculate_stats(results_df, license_key, buyers)
                stats_tracker.add_stats(stats)
            else:
                failed += 1

    # Final summary
    print(f"\n{'='*60}")
    print("🏁 Batch Processing Complete!")
    print(f"{'='*60}")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {successful + failed}")

    if successful > 0:
        print(f"\n💾 Results saved in: results_per_key/ folder")
        print(f"📁 Check individual CSV files for each license key analysis")
        print(f"📊 Statistics saved in: stats_per_license.csv")

        # Print statistics summary
        stats_tracker.print_summary()

    return 0 if failed == 0 else 1


if __name__ == '__main__':
    sys.exit(main())
