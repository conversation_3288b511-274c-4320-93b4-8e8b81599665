# Optional dependencies for performance optimization
# Install with: pip install -r requirements_optimization.txt

# Core optimization dependencies
numba>=0.56.0          # JIT compilation for mathematical operations
matplotlib>=3.5.0      # Performance visualization charts
seaborn>=0.11.0        # Enhanced statistical visualizations

# Additional performance libraries (optional)
# These can provide further performance improvements
cython>=0.29.0         # Alternative to Numba for C extensions
psutil>=5.8.0          # System resource monitoring
memory_profiler>=0.60.0  # Memory usage profiling

# Development and testing
pytest>=7.0.0          # Unit testing framework
pytest-benchmark>=4.0.0  # Performance testing utilities
