#!/usr/bin/env python3
"""
Item Journey Analyzer - Main script
"""

import argparse
import sys

import pandas as pd

from data_fetcher import DataFetcher
from data_processor import DataProcessor
from cache_manager import CacheManager


class ItemJourneyAnalyzer:
    def __init__(self, cache_dir: str = "cache"):
        self.cache_manager = CacheManager(cache_dir)
        self.data_fetcher = DataFetcher(self.cache_manager)
        self.data_processor = DataProcessor()

    def analyze_item_journey(self, license_prefix: str = "0722-725C", buyer_names: str = "-t",
                           start_date: str = "2025-06-01", end_date: str = "2025-07-01",  # 5 days only
                           max_cache_age: int = 24, clear_cache: bool = False) -> pd.DataFrame:
        """
        Main analysis method that replicates the SQL functionality.
        """
        if clear_cache:
            self.cache_manager.clear_cache()

        print(f"🔍 Analyzing item journey for license prefix '{license_prefix}', buyers '{buyer_names}'")
        print(f"📅 Date range: {start_date} to {end_date}")

        # Fetch all required data with caching
        print("\n📊 Fetching data...")
        server_items = self.data_fetcher.fetch_server_items(license_prefix, start_date, end_date, max_cache_age)
        clicks_purchases = self.data_fetcher.fetch_clicks_and_purchases(license_prefix, start_date, end_date, max_cache_age)
        sold_items = self.data_fetcher.fetch_sold_items(buyer_names, start_date, end_date, max_cache_age)
        epn_transactions = self.data_fetcher.fetch_epn_transactions(start_date, end_date, max_cache_age)

        # Process data step by step
        print("\n🔄 Processing data...")

        # Step 1: Unnest pushed_to_users array and filter by license prefix
        expanded_server = self.data_processor.unnest_pushed_to_users(server_items, license_prefix)
        if expanded_server.empty:
            print("❌ No server items found with the specified license prefix")
            return pd.DataFrame()

        # Step 2: Join with clicks and purchases
        with_clicks = self.data_processor.join_clicks_and_purchases(expanded_server, clicks_purchases)

        # Step 3: Join with sold items (inner join - only items that were sold)
        with_sold = self.data_processor.join_sold_items(with_clicks, sold_items)
        if with_sold.empty:
            print("❌ No sold items found matching the criteria")
            return pd.DataFrame()

        # Step 4: Join with EPN transactions (left join - optional)
        with_epn = self.data_processor.join_epn_transactions(with_sold, epn_transactions)

        # Step 5: Add action ranking
        ranked_data = self.data_processor.add_action_ranking(with_epn)

        # Step 6: Calculate time intervals
        with_times = self.data_processor.calculate_time_intervals(ranked_data)

        # Step 7: Apply final filtering and sorting
        final_result = self.data_processor.apply_final_filtering_and_sorting(with_times)

        print(f"\n✅ Analysis complete: {len(final_result)} item journeys found")
        return final_result

    def close_connection(self):
        """Close database connection."""
        self.data_fetcher.close_connection()


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(
        description="Item Journey Analyzer - Python equivalent of item_journey_by_license_key.sql",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_analyzer.py
  python main_analyzer.py --license-prefix "4B4F" --buyer-names "john,mary"
  python main_analyzer.py --clear-cache --output results.csv
        """
    )

    parser.add_argument(
        '--license-prefix',
        default='0722-725C',
        help='License key prefix to filter by (default: "3A3E")'
    )

    parser.add_argument(
        '--buyer-names',
        default='-t',
        help='Buyer names to filter by, comma-separated (default: "le")'
    )

    parser.add_argument(
        '--start-date',
        default='2025-06-01',
        help='Start date in YYYY-MM-DD format (default: "2025-06-01")'
    )

    parser.add_argument(
        '--end-date',
        default='2025-07-01',  # 5 days only
        help='End date in YYYY-MM-DD format (default: "2025-06-06" - 5 days)'
    )

    parser.add_argument(
        '--cache-dir',
        default='cache',
        help='Directory for cached data (default: "cache")'
    )

    parser.add_argument(
        '--clear-cache',
        action='store_true',
        help='Clear cached data before processing'
    )

    parser.add_argument(
        '--output',
        help='Save results to CSV file'
    )

    parser.add_argument(
        '--max-cache-age',
        type=int,
        default=24,
        help='Maximum cache age in hours (default: 24)'
    )

    return parser


def display_results(df: pd.DataFrame) -> None:
    """Display results in a formatted table."""
    if df.empty:
        print("\n📋 No results found.")
        return

    print(f"\n📋 Results ({len(df)} items):")
    print("=" * 120)

    # Format the display
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', 120)
    pd.set_option('display.max_colwidth', 20)

    # Round numeric columns for better display
    display_df = df.copy()
    numeric_cols = ['found_time', 'time_clicked', 'time_sold']
    for col in numeric_cols:
        if col in display_df.columns:
            display_df[col] = display_df[col].round(2)

    print(display_df.to_string(index=False))
    print("=" * 120)


def main():
    """Main execution function."""
    parser = create_argument_parser()
    args = parser.parse_args()

    print("🚀 Item Journey Analyzer")
    print("=" * 50)

    try:
        # Initialize analyzer
        analyzer = ItemJourneyAnalyzer(cache_dir=args.cache_dir)

        # Show cache info if not clearing
        if not args.clear_cache:
            cache_info = analyzer.cache_manager.get_cache_info()
            if cache_info:
                print("\n💾 Cache status:")
                for filename, info in cache_info.items():
                    status = "✓ Valid" if info['valid'] else "⚠️ Expired"
                    print(f"  {filename}: {info['size_mb']}MB, {info['age_hours']}h old {status}")

        # Run analysis
        results = analyzer.analyze_item_journey(
            license_prefix=args.license_prefix,
            buyer_names=args.buyer_names,
            start_date=args.start_date,
            end_date=args.end_date,
            max_cache_age=args.max_cache_age,
            clear_cache=args.clear_cache
        )

        # Display results
        display_results(results)

        # Create results_per_key folder if it doesn't exist
        import os
        results_folder = "results_per_key"
        os.makedirs(results_folder, exist_ok=True)

        # Always save to CSV (default filename if not specified)
        if not results.empty:
            if args.output:
                # If user specified output, save to results_per_key folder
                output_file = os.path.join(results_folder, args.output)
            else:
                # Default filename in results_per_key folder
                output_file = os.path.join(results_folder, f"item_journey_results_{args.license_prefix}_{args.start_date}_to_{args.end_date}.csv")

            try:
                results.to_csv(output_file, index=False)
                print(f"\n💾 Results saved to {output_file}")
            except Exception as e:
                print(f"\n❌ Failed to save results: {e}")

        # Also save to user-specified file if different from default (both in results_per_key folder)
        default_filename = os.path.join(results_folder, f"item_journey_results_{args.license_prefix}_{args.start_date}_to_{args.end_date}.csv")
        if args.output and not results.empty:
            user_output_file = os.path.join(results_folder, args.output)
            if user_output_file != default_filename:
                try:
                    results.to_csv(user_output_file, index=False)
                    print(f"\n💾 Results also saved to {user_output_file}")
                except Exception as e:
                    print(f"\n❌ Failed to save to {user_output_file}: {e}")

        # Close database connection
        analyzer.close_connection()

    except KeyboardInterrupt:
        print("\n\n⏹️ Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
